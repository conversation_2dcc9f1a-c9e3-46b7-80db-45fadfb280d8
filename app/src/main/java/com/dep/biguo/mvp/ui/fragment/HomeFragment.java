package com.dep.biguo.mvp.ui.fragment;

import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.ImageView;

import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.ArticleBean;
import com.dep.biguo.bean.CircleBean;
import com.dep.biguo.bean.CourseBean;
import com.dep.biguo.bean.JoinWechatGroupBean;
import com.dep.biguo.bean.TruePaperAllBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.bean.ZkNewHomeBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.HomeBottomViewBinding;
import com.dep.biguo.databinding.HomeFragmentBinding;
import com.dep.biguo.di.component.DaggerHomeComponent;
import com.dep.biguo.dialog.AppTypePopupWindow;
import com.dep.biguo.dialog.CircleCommentDialog;
import com.dep.biguo.dialog.ExamScoreDialog;
import com.dep.biguo.dialog.PopupDialog;
import com.dep.biguo.dialog.SelectCourseDialog;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.mvp.contract.HomeContract;
import com.dep.biguo.mvp.presenter.HomePresenter;
import com.dep.biguo.mvp.ui.activity.CircleDetailActivity;
import com.dep.biguo.mvp.ui.activity.CircleTopicActivity;
import com.dep.biguo.mvp.ui.activity.CityActivity;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.activity.MyCouponActivity;
import com.dep.biguo.mvp.ui.activity.ProfessionSchoolActivity;
import com.dep.biguo.mvp.ui.adapter.CircleFlowAdapter;
import com.dep.biguo.mvp.ui.adapter.CirclePublishAdapter;
import com.dep.biguo.mvp.ui.adapter.HomeShareAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.ShareUtil;
import com.dep.biguo.utils.StartFinal;
import com.biguo.utils.util.TintDrawableUtil;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.database.util.RealQuery;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.widget.HomeNewHeadView;
import com.dep.biguo.widget.ItemDecoration;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.widget.FloatingImageView;
import com.dep.biguo.widget.ToolBar;
import com.dep.biguo.wxapi.WxMinApplication;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;

import org.simple.eventbus.Subscriber;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * ......................我佛慈悲.....................
 * ......................_oo0oo_.....................
 * .....................o8888888o....................
 * .....................88" . "88....................
 * .....................(| -_- |)....................
 * .....................0\  =  /0....................
 * ...................___/`---'\___..................
 * ..................' \\|     |// '.................
 * ................./ \\|||  :  |||// \..............
 * .............../ _||||| -卍-|||||- \..............
 * ..............|   | \\\  -  /// |   |.............
 * ..............| \_|  ''\---/''  |_/ |.............
 * ..............\  .-\__  '-'  ___/-. /.............
 * ............___'. .'  /--.--\  `. .'___...........
 * .........."" '<  `.___\_<|>_/___.' >' ""..........
 * ........| | :  `- \`.;`\ _ /`;.`/ - ` : | |.......
 * ........\  \ `_.   \_ __\ /__ _/   .-` /  /.......
 * ....=====`-.____`.___ \_____/___.-`___.-'=====....
 * ......................`=---='.....................
 * ..................佛祖保佑，永无BUG.................．
 * ...................南无安卓工程佛....................
 */

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/21
 * @Description: 首页
 */
public class HomeFragment extends BaseFragment<HomePresenter> implements HomeContract.View {
    private HomeFragmentBinding binding;
    private HomeNewHeadView homeHeadView;
    private HomeBottomViewBinding homeBottomBinding;
    private CircleFlowAdapter homeAdapter;
    private HomeShareAdapter mShareAdapter;//分享

    private AppTypePopupWindow mAppTypePopupWindow;//切换证书的弹窗
    private ToolBar toolBar;//顶部标题
    private SelectCourseDialog selectCourseDialog;

    public static HomeFragment newInstance() {
        return new HomeFragment();
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        DaggerHomeComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.home_fragment, null, false);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);
        homeHeadView = binding.homeHeadView;//new HomeNewHeadView(getContext(), mPresenter);
        homeHeadView.setmPresenter(mPresenter);
        initToolBar();

        homeAdapter = new CircleFlowAdapter();
        homeAdapter.setOnItemClickListener((baseQuickAdapter, itemView, i) -> {
            CircleDetailActivity.start(getContext(), homeAdapter.getItem(i));
        });

        homeAdapter.setOnItemChildClickListener(new CircleFlowAdapter.OnItemChildClickListener() {
            @Override
            public void good(CircleBean.Moment publish) {
                if(!MainAppUtils.checkLogin(getContext())) return;
                mPresenter.momentGoodOrShare(publish, 0);
            }

            @Override
            public void moreOption(View targetView, CircleBean.Moment moment) {
                if(!MainAppUtils.checkLogin(getContext())) return;
                showMoreOptionDialog(targetView, moment);
            }
        });

        //设置下拉刷新监听
        binding.swipeView.bindAdapter(homeAdapter, binding.recyclerView, page -> {
            if(page == 1){
                mPresenter.getHomeData(UserCache.getHomeCode(), false);
                //刷新是否有可用的优惠券
                mPresenter.getDiscountStatus();
                //刷新考试资讯/学校新闻
                homeHeadView.shoExamNews();
            }
            LogUtil.d("dddd", "下拉刷新");
            mPresenter.getCircle(1);
        });

        //添加分割线
        binding.recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal)
                .addSkipDraw(1)
                .setLeftMargin(15)
                .setRightMargin(15));
        //监听面板滚动结束时，拖动按钮从屏幕外滑动进入
        binding.recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            private int oldState;
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                //从1到2是拖拽变滚动，//从2到1是滚动变拖拽，这两种情况都不继续往下走，免得重复执行动画
                if(oldState == 1 && newState == 2 || oldState == 2 && newState == 1) {
                    oldState = newState;
                    return;
                }
                oldState = newState;

                moveUpAnimation(binding.drag1View, newState != RecyclerView.SCROLL_STATE_IDLE);
                moveUpAnimation(binding.drag2View, newState != RecyclerView.SCROLL_STATE_IDLE);
                moveUpAnimation(binding.drag3View, newState != RecyclerView.SCROLL_STATE_IDLE);
            }
        });

        initBottom();

        // --- 预览代码开始 ---
        UserBean previewUser = new UserBean();
        previewUser.setNickname("昵称最多展示九个字");
        previewUser.setAvatar("https://img.biguotk.com/img/2023022114255563f465d3e7530.png"); // 使用一个在线图片URL

        ImageView avatar = binding.vipCardContainer.findViewById(R.id.iv_avatar);
        TextView nickname = binding.vipCardContainer.findViewById(R.id.tv_nickname);
        TextView vipDuration = binding.vipCardContainer.findViewById(R.id.tv_vip_duration);

        ImageLoader.loadAvatar(avatar, previewUser.getAvatar());
        nickname.setText(previewUser.getNickname());
        vipDuration.setText("会员卡权益已陪伴你21天");
        // --- 预览代码结束 ---

        return binding.getRoot();
    }

    /**显示分享弹窗
     * @param moment 动态
     */
    private void showShareDialog(CircleBean.Moment moment){
        Object loadObj = !AppUtil.isEmpty(moment.getFiles()) ? moment.getFiles().get(0).getCover_url() : R.drawable.app_icon;
        ShareDialog.loadImageShare(getContext(), loadObj, resource -> {
            Activity activity = AppManager.getAppManager().getTopActivity();
            if(activity == null) return;

            new ShareDialog.Builder(activity)
                    .setShareTitle(moment.getContent())
                    .setShareContent(String.format("来自%s的动态", moment.getNickname()))
                    .setShareBitmap(resource, ShareUtil.SHARE_TYPE.LINK)
                    .setShareUrl(String.format("%s?posts_id=%s", Constant.CIRCLE_DETAIL, moment.getPosts_id()))
                    .setOnShareListener(type -> mPresenter.momentGoodOrShare(moment, 1))
                    .builder()
                    .show();
        });
    }

    public void showMoreOptionDialog(View targetView, CircleBean.Moment moment){
        //发布者是否是当前登录的用户
        boolean isThisUserSend = UserCache.getUserCache() != null && UserCache.getUserCache().getUser_id() == moment.getUsers_id();

        new PopupDialog.Builder<>(getContext(), isThisUserSend ? "删除" : "举报")
                .setForeachData(PopupDialog.ForeachData.getForeachDataInstance())
                .setOnItemClickListener((itemView, data, position) -> {
                    if(!MainAppUtils.checkLogin(getContext())) return;

                    if(TextUtils.equals("删除", data)){
                        new MessageDialog.Builder(getChildFragmentManager())
                                .setTitle("温馨提醒")
                                .setContent("是否删除动态？")
                                .setNegativeText("取消")
                                .setPositiveText("删除")
                                .setPositiveClickListener(v -> mPresenter.deleteMoment(moment))
                                .builder()
                                .show();

                    }else if(TextUtils.equals("举报", data)) {
                        new CircleCommentDialog(getContext())
                                .setOnSendListener((isSend, content) -> {
                                    if(!isSend) return;
                                    mPresenter.reportMoment(moment.getPosts_id(), content);
                                })
                                .setHint("请输入您举报原因")
                                .show();
                    }
                })
                .setOrientation(LinearLayoutCompat.HORIZONTAL)
                .setArrowXAnchor(PopupDialog.ARROW_END)
                .setArrowOffsetX(-DisplayHelper.dp2px(getContext(), 9))
                .build()
                .showAsDropDown(targetView, 0, -DisplayHelper.dp2px(getContext(), 12));
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {

    }

    public void initToolBar(){
        toolBar = new ToolBar.Builder(getContext(), binding.rootView)
                .setTarget(ToolBar.Builder.LEFT)
                .setDrawablesRes(new int[]{R.drawable.home_address_city, 0, 0, 0})
                .setDrawablePadding(0)
                .setTextMaxLength(5)
                .setTextColor(R.color.tblack)
                .setOnClickListener(v -> ArmsUtils.startActivity(CityActivity.class))
                .setTarget(ToolBar.Builder.TITLE)
                .setOnClickListener(v -> mPresenter.getCourse())
                .setViewLayout(R.layout.home_tool_bar)
                .setTarget(ToolBar.Builder.RIGHT)
                .setText(MainAppUtils.getAppTypeName())
                .setDrawablesRes(new int[]{0, 0, R.drawable.home_select_cert, 0})
                .setDrawablePadding(2)
                .setTextColor(R.color.tblack)
                .setOnClickListener(v -> {
                    if (!checkPopupDismiss()) return;
                    if (mAppTypePopupWindow == null) {
                        mAppTypePopupWindow = new AppTypePopupWindow(getActivity());
                        mAppTypePopupWindow.setHeight(binding.swipeView.getHeight() + DisplayHelper.dp2px(getContext(), 44));
                    }
                    mAppTypePopupWindow.showAsDropDown(toolBar.getLayoutView(), 0, 0);
                })
                .build();

        //设置选择专业按钮的图标颜色，由于显示出来之前还没有初始化xml中设置的图片，因此getCompoundDrawables()返回的图片会全是null，需要手动设置一下
        TextView courseView = toolBar.getViewById(R.id.professionView);
        TextDrawableLoader.loadRight(getContext(), courseView, R.drawable.arrow_black_down);
        TintDrawableUtil.EndTintDrawable(courseView, getResources().getColor(R.color.tblack));
    }

    public void initBottom(){
        homeBottomBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.home_bottom_view, null, false);
        homeAdapter.setFooterView(homeBottomBinding.getRoot());

        mShareAdapter = new HomeShareAdapter(new ArrayList<>());
        mShareAdapter.bindToRecyclerView(homeBottomBinding.shareRecyclerView);
        mShareAdapter.setOnItemClickListener((adapter, view, position) -> {
            new UmengEventUtils(getContext())
                    .addParams("path", "APP：自考-》首页-》分享好友")
                    .addParams("platform", mShareAdapter.getShareName(position))
                    .pushEvent(UmengEventUtils.CLICK_SHARE_APP);
            ShareUtil.Share(new ShareUtil.Builder(getActivity(), ShareUtil.mediaValueArray[position]));
        });

        homeBottomBinding.backView.setOnClickListener(v -> {
            //使用外部浏览器打开
            new MessageDialog.Builder(getChildFragmentManager())
                    .setTitle("温馨提醒")
                    .setContent("即将跳转至浏览器，是否继续？")
                    .setNegativeText("取消")
                    .setPositiveText("继续")
                    .setPositiveClickListener(v1 -> {
                        Uri uri = Uri.parse("https://beian.miit.gov.cn/");
                        Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                        startActivity(intent);
                    }).builder()
                    .show();
        });
    }

    @Override
    public void setHomeData(ZkNewHomeBean zkHomeBean) {

        //创建课程，缓存到本地数据库，方便管理该课程缓存的题库
        CourseBean courseBean = new CourseBean();
        courseBean.setCourses_not_joined(zkHomeBean.getCourses());
        RealQuery.insertCourse(courseBean);

        homeHeadView.setHomeData(zkHomeBean);
    }

    @Override
    public void setNewsData(List<ArticleBean> list) {
        homeHeadView.setNews(list);
    }

    @Override
    public void notifyNews() {
        homeHeadView.notifyNews();
    }

    @Override
    public void getCourseSuccess(CourseBean data) {
        if(selectCourseDialog == null) {
            selectCourseDialog = new SelectCourseDialog(getContext())
                    .setWriteScoreListener(item -> {
                        new ExamScoreDialog(getContext())
                                .setCode(item.getCode())
                                .setName(item.getName())
                                .setOnCommitListener(score -> mPresenter.setScore(item, score))
                                .show();
                    })
                    .setJoinOnClickListener((fromList, item) -> mPresenter.manageCourse(fromList, null, null, item))
                    .setUnJoinOnClickListener((fromList, item) -> mPresenter.manageCourse(fromList, item, null, null))
                    .setPassOnClickListener((fromList, item) -> mPresenter.manageCourse(fromList, null, item, null))
                    .setSelectCourseListener(item -> {
                        //当选了课程，就认为不再需要强制显示报考弹窗
                        UserCache.cacheHomeShowEnrollCourse();
                        mPresenter.getHomeData(item.getCode(), true);
                        selectCourseDialog.dismiss();
                    });
            selectCourseDialog.setHeight(binding.swipeView.getHeight() + DisplayHelper.dp2px(getContext(), 44));
        }
        selectCourseDialog.setCourseBean(data)
            .showAsDropDown(toolBar.getLayoutView(), 0, 0);
    }

    @Override
    public void manageCourseSuccess() {
        if(selectCourseDialog != null){
            selectCourseDialog.manageCourseSuccess();
        }
    }

    @Override
    public void getCircleDataFail() {
        if(AppUtil.isEmpty(homeAdapter.getData())){
            homeHeadView.setShowCircleTitleView(false);
        }
        binding.swipeView.finishLoadMore(false);
        binding.swipeView.setEnableLoadMore(false);
    }

    @Override
    public void getCircleDataSuccess(List<CircleBean.Moment> list) {
        homeAdapter.setNewData(list);
        binding.swipeView.finishLoadMore(true, false);
        binding.swipeView.setEnableLoadMore(false);

        homeHeadView.setShowCircleTitleView(!AppUtil.isEmpty(homeAdapter.getData()));
    }

    @Override
    public void shareSuccess(CircleBean.Moment moment) {
        moment.setRelay_count(moment.getRelay_count() + 1);
        //因为适配器添加了一个HeaderView，所以刷新的目标要多往后移1个位置
        homeAdapter.notifyItemChanged(homeAdapter.getHolderPositionByItemData(moment));
    }

    @Override
    public void deleteComment(CircleBean.Moment moment) {
        int itemPosition = homeAdapter.getHolderPositionByItemData(moment);
        homeAdapter.getData().remove(moment);
        homeAdapter.notifyItemRemoved(itemPosition);
    }

    @Override
    public void closeShowSelectCourse(){
        if(selectCourseDialog != null) {
            selectCourseDialog.dismiss();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if(UserCache.getCity() == null){
            ArmsUtils.startActivity(CityActivity.class);
            return;
        }else if(UserCache.getProfession() == null){
            ProfessionSchoolActivity.Start(getContext());
            return;
        }
        //获取缓存中的省份、专业、原专业
        String newCityName = AppUtil.isEmpty(UserCache.getCity().getCity_name(), "");
        String newProfessionName = AppUtil.isEmpty(UserCache.getProfession().getName(), "");
        String newBeforeNewProfessionName = AppUtil.isEmpty(UserCache.getProfession().getOld_name(), "");

        //获取toolBar中的专业组件和原专业组件
        TextView cityName = toolBar.getViewByTarget(ToolBar.Builder.LEFT);
        TextView professionView = toolBar.getViewById(R.id.professionView);
        TextView beforeProfessionView = toolBar.getViewById(R.id.beforeProfessionView);
        //获取界面中显示的省份、专业、原专业
        String selectedProvinceName = cityName.getText().toString();
        String selectedProfessionName = professionView.getText().toString();
        String selectedBeforeProfessionName = beforeProfessionView.getText().toString().replace("原专业：","");

        //当缓存中的"省份"、"专业"、"原专业"至少有一个不相同时，说明有变化，需要重新请求后台数据
        //新人活动持续时间比较短暂，所以可以在登录账号属于新用户这一段时间，要保持数据最新
        if((!selectedProvinceName.equals(newCityName)
                || !selectedProfessionName.equals(newProfessionName)
                || !selectedBeforeProfessionName.equals(newBeforeNewProfessionName))
                || homeHeadView.homeViewBinding.newUserActivityView.getVisibility() == View.VISIBLE){

            //设置省份
            toolBar.getViewByTarget(ToolBar.Builder.LEFT).setText(newCityName);

            //设置专业名
            professionView.setText(newProfessionName);
            //当有原专业字段时，需要对原专业的控件进行调整
            if(!TextUtils.isEmpty(UserCache.getProfession().getOld_name())) {
                beforeProfessionView.setText(String.format("原专业：%s", newBeforeNewProfessionName));
                beforeProfessionView.setVisibility(View.VISIBLE);
            }else {
                beforeProfessionView.setText("");
                beforeProfessionView.setVisibility(View.GONE);
            }

            //刷新首页数据，每次启动APP，也会进入到此处
            mPresenter.getHomeData(UserCache.getHomeCode(), true);
            //刷新考试资讯/学校新闻
            homeHeadView.shoExamNews();
            //获取圈子第一页
            if(AppUtil.isEmpty(homeAdapter.getData())) {
                LogUtil.d("dddd", "onResume刷新");
                mPresenter.getCircle(1);
            }
        }
        //刷新是否有可用的优惠券
        mPresenter.getDiscountStatus();

        if(UserCache.getUserCache() == null){
            //未登录的时候，跳转微信小程序
            binding.drag1View.setBackgroundResource(R.drawable.drag2);
        }

        setJoinWechatGroupDrag(UserCache.getZkHomeJoinWechatGroup() != null);

    }

    /**
     * 当屏幕尺寸发生变化时调用（小窗或分屏）
     */
    public void screenSizeChange(){
        if(toolBar == null) return;
        toolBar.screenSizeChange();
    }
    public void onClick(View view) {
        if(view == binding.drag1View){//拖动按钮1
            //埋点联系客服按钮的点击次数
            new UmengEventUtils(getContext())
                    .pushEvent(UmengEventUtils.CLICK_CUSTOMER_SERVICE);

            WxMinApplication.StartWechat(getContext(), false);

        }else if(view == binding.drag3View){//拖动按钮2
            if(!MainAppUtils.checkLogin(getContext())) return;
            JoinWechatGroupBean groupBean = UserCache.getZkHomeJoinWechatGroup();

            //埋点统计领取资料的点击次数
            new UmengEventUtils(getContext())
                    .pushEvent(UmengEventUtils.ENTER_USER_INFO_MANAGE);

            if(!TextUtils.isEmpty(groupBean.getXcx_path())) {//跳转小程序
                WxMinApplication.StartWechat(getContext(), groupBean.getXcx_path(), groupBean.getUrl(), false);

            }else if(!TextUtils.isEmpty(groupBean.getUrl())){//跳转H5页面, 我要报名、积分入户、学梦计划
                HtmlActivity.start(getActivity(), groupBean.getUrl());
            }

        }else if(view == binding.drag2View){//拖动按钮2
            if(!MainAppUtils.checkLogin(getContext())) return;
            ArmsUtils.startActivity(MyCouponActivity.class);

        }else if(view == binding.closeDrag2View){//关闭拖动按钮2
            UserCache.setDayCloseZkHomeDiscount();
            binding.drag2View.hide();
        }
    }


    @Override
    public void refreshFinish() {
        //设置下拉刷新结束
        binding.swipeView.finishRefresh(false);
    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }

    @Subscriber(tag = EventBusTags.LOGIN_SUCCESS)
    private void loginSuccess(UserBean user) {
        //登录后，更新首页数据
        binding.swipeView.autoRefresh();
    }

    @Subscriber(tag = EventBusTags.LOGOUT_SUCCESS)
    private void logoutSuccess(UserBean user) {
        //登录后，更新首页数据
        binding.swipeView.autoRefresh();
    }

    /**
     *切换专业的时候重新提交调查标签
     * @param user
     */
    @Subscriber(tag = EventBusTags.UPDATE_LABEL)
    private void reSetLabel(UserBean user) {
        onResume();
    }

    /**
     *切换课程
     * @param
     */
    @Subscriber(tag = EventBusTags.CHANGE_COURSE)
    private void reSetLabel(CourseBean.CourseItemBean courseItemBean) {
        mPresenter.getHomeData(courseItemBean.getCode(), true);
    }

    @Subscriber(tag = EventBusTags.CLEAR_HOME_POPUP_WINDOW_COURSE)
    public void clearCourse(String clear){
        mPresenter.clearCourse();
    }

    public void isShowDiscountDrag2View(int expireTime) {
        //登录后，根据是否有优惠券，显示或隐藏优惠券浮动按钮
        if(!UserCache.isDayCloseZkHomeDiscount()){
            binding.drag2View.setVisibility(expireTime > 0 ? View.VISIBLE : View.GONE);
            //有72小时后到期的优惠券，则显示GIF图提示用户
            if(expireTime < (72 * 60 * 60)){
                ImageLoader.loadImageNoPlaceholder(binding.drag2ImageView, "https://file.biguotk.com/img/2023090113420764f179af16730.gif", R.drawable.zk_will_expire_discount);
            }else {
                binding.drag2ImageView.setImageResource(R.drawable.zk_home_discount);
            }
        }else {
            binding.drag2View.setVisibility(View.GONE);
        }
    }

    public void setJoinWechatGroupDrag(boolean isShow){
        //没有缓存到加入微信群的数据，则不显示
        if(binding != null) {
            binding.drag3View.setVisibility(isShow ? View.VISIBLE : View.GONE);
        }
    }

    /**页面停止滚动时，拖动按钮从屏幕外滑动进来
     * @param view
     */
    private void moveUpAnimation(FloatingImageView view, boolean isScrolling) {
        float hideWidth = view.getMeasuredWidth()/3f*2;
        float start = view.getOrientation() > 0 ? hideWidth : (-DisplayHelper.getWindowWidth(getContext()) + view.getMeasuredWidth()-hideWidth);
        float end = view.getOrientation() > 0 ? 0 : -view.getLeft();

        if(isScrolling){
            ObjectAnimator.ofFloat(view, "translationX", end, start).setDuration(400).start();
        }else {
            ObjectAnimator.ofFloat(view, "translationX", start, end).setDuration(400).start();
        }
    }

    /**检查切换证书的弹窗是否已消失
     * @return
     */
    public boolean checkPopupDismiss() {
        if (mAppTypePopupWindow != null && mAppTypePopupWindow.isShowing()) {
            mAppTypePopupWindow.dismiss();
            return false;
        }
        return true;
    }

    @Subscriber(tag = EventBusTags.VIP_OPEN_SUCCESS)
    public void onVipOpenSuccess(boolean success) {
        if (success && binding != null) {
            binding.vipCardContainer.setVisibility(View.VISIBLE);
            UserBean userBean = UserCache.getUserCache();
            if(userBean != null) {
                ImageView avatar = binding.vipCardContainer.findViewById(R.id.iv_avatar);
                TextView nickname = binding.vipCardContainer.findViewById(R.id.tv_nickname);
                TextView vipDuration = binding.vipCardContainer.findViewById(R.id.tv_vip_duration);

                ImageLoader.loadAvatar(avatar, userBean.getAvatar());
                nickname.setText(userBean.getNickname());
                // TODO: Get real vip duration
                vipDuration.setText("会员卡权益已陪伴你0天");
            }
        }
    }

    @Override
    public void setData(@Nullable Object data) {

    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(requestCode == StartFinal.REQUEST_CODE && resultCode == FragmentActivity.RESULT_OK){
            //有购买记录需要刷新一下
            binding.swipeView.autoRefresh();
        }
    }
}