<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.fragment.HomeFragment" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/rootView"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.dep.biguo.widget.SmartRefreshLayout
            android:id="@+id/swipeView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.google.android.material.appbar.AppBarLayout
                    android:id="@+id/appBarLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_behavior="com.yuruiyin.appbarlayoutbehavior.AppBarLayoutBehavior"
                    app:elevation="0dp">

                    <FrameLayout
                        android:id="@+id/vipCardContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="visible"
                        app:layout_scrollFlags="scroll"
                        tools:visibility="visible">
                        <include layout="@layout/item_vip_card_view" />
                    </FrameLayout>

                    <com.dep.biguo.widget.HomeNewHeadView
                        android:id="@+id/homeHeadView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_scrollFlags="scroll"/>

                </com.google.android.material.appbar.AppBarLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingHorizontal="10dp"
                    android:background="@drawable/error_coll_bg"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior"
                    tools:itemCount="4"
                    tools:listitem="@layout/circle_flow_item"
                    app:spanCount="2"
                    app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager" />

            </androidx.coordinatorlayout.widget.CoordinatorLayout>
        </com.dep.biguo.widget.SmartRefreshLayout>

        <com.biguo.utils.widget.FloatingImageView
            android:id="@+id/drag1View"
            android:background="@drawable/drag2"
            android:layout_width="58dp"
            android:layout_height="58dp"
            android:layout_marginBottom="20dp"
            android:onClick="@{onClickListener.onClick}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <com.biguo.utils.widget.FloatingImageView
            android:id="@+id/drag3View"
            android:background="@drawable/join_wechat_group"
            android:layout_width="58dp"
            android:layout_height="58dp"
            android:visibility="gone"
            android:layout_marginBottom="10dp"
            android:onClick="@{onClickListener.onClick}"
            app:layout_constraintBottom_toTopOf="@id/drag1View"
            app:layout_constraintEnd_toEndOf="parent"/>

        <com.biguo.utils.widget.FloatingImageView
            android:id="@+id/drag2View"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:visibility="gone"
            android:onClick="@{onClickListener.onClick}"
            app:layout_constraintBottom_toTopOf="@id/drag3View"
            app:layout_constraintEnd_toEndOf="parent" >

            <ImageView
                android:id="@+id/drag2ImageView"
                android:background="@drawable/zk_home_discount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <ImageView
                android:id="@+id/closeDrag2View"
                android:src="@drawable/zk_home_discount_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="@{onClickListener.onClick}"
                app:layout_constraintEnd_toEndOf="@id/drag2ImageView"
                app:layout_constraintTop_toTopOf="@id/drag2ImageView"
                app:layout_constraintBottom_toTopOf="@id/drag2ImageView"/>
        </com.biguo.utils.widget.FloatingImageView>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
